'use client';

import { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { mainNavItems } from '@/data/navigationLinks';

export default function OldMoneyNavbar() {
  const [scrolled, setScrolled] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const pathname = usePathname();

  const handleScroll = useCallback(() => {
    const currentScrollY = window.scrollY;
    setScrolled(currentScrollY > 20);
  }, []);

  useEffect(() => {
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  useEffect(() => {
    setIsMenuOpen(false);
  }, [pathname]);

  const isActiveLink = useCallback((href) => {
    if (href === '/') return pathname === '/';
    return pathname.startsWith(href);
  }, [pathname]);

  return (
    <>
      <nav
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ease-gentle ${
          scrolled 
            ? 'bg-sanctuary/95 backdrop-blur-md shadow-elegant border-b border-border-subtle' 
            : 'bg-transparent'
        }`}
        role="navigation"
        aria-label="Główna nawigacja"
      >
        <div className="max-w-7xl mx-auto px-6 lg:px-12">
          <div className="flex items-center justify-between h-20">
            
            {/* Logo - Old Money elegance */}
            <Link
              href="/"
              className="group relative font-serif font-light text-2xl tracking-wider text-charcoal transition-all duration-normal hover:text-enterprise-brown"
            >
              BAKASANA
              <span className="absolute -bottom-1 left-0 w-0 h-[1px] bg-enterprise-brown transition-all duration-normal group-hover:w-full"></span>
            </Link>

            {/* Desktop Menu - Old Money spacing */}
            <div className="hidden lg:flex items-center space-x-12">
              {mainNavItems.slice(1).map((item, index) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`group relative font-serif font-light text-base tracking-wide transition-all duration-normal ${
                    isActiveLink(item.href)
                      ? 'text-enterprise-brown'
                      : 'text-charcoal hover:text-enterprise-brown'
                  }`}
                >
                  {item.label}
                  <span 
                    className={`absolute -bottom-1 left-0 h-[1px] bg-enterprise-brown transition-all duration-normal ${
                      isActiveLink(item.href) ? 'w-full' : 'w-0 group-hover:w-full'
                    }`}
                  ></span>
                </Link>
              ))}
            </div>

            {/* CTA Button - Ghost style Old Money */}
            <div className="hidden lg:flex items-center">
              <Link
                href="/rezerwacja"
                className="group inline-flex items-center gap-2 px-8 py-3 border border-enterprise-brown text-enterprise-brown font-serif font-medium tracking-wide transition-all duration-normal hover:bg-enterprise-brown hover:text-sanctuary hover:scale-[1.02] hover:shadow-elegant"
              >
                Rezerwuj
                <svg 
                  className="w-4 h-4 transition-transform duration-normal group-hover:translate-x-1" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Link>
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="lg:hidden p-2 text-charcoal hover:text-enterprise-brown transition-colors duration-normal"
              aria-label="Toggle menu"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {isMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Menu - Old Money elegance */}
        <div className={`lg:hidden transition-all duration-normal ease-gentle ${
          isMenuOpen 
            ? 'max-h-screen opacity-100' 
            : 'max-h-0 opacity-0 overflow-hidden'
        }`}>
          <div className="bg-sanctuary/98 backdrop-blur-md border-t border-border-subtle">
            <div className="px-6 py-8 space-y-6">
              {mainNavItems.slice(1).map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`block font-serif font-light text-lg tracking-wide transition-all duration-normal ${
                    isActiveLink(item.href)
                      ? 'text-enterprise-brown border-l-2 border-enterprise-brown pl-4'
                      : 'text-charcoal hover:text-enterprise-brown hover:pl-4'
                  }`}
                >
                  {item.label}
                </Link>
              ))}
              
              {/* Mobile CTA */}
              <div className="pt-6 border-t border-border-subtle">
                <Link
                  href="/rezerwacja"
                  className="block w-full text-center py-4 border border-enterprise-brown text-enterprise-brown font-serif font-medium tracking-wide transition-all duration-normal hover:bg-enterprise-brown hover:text-sanctuary"
                >
                  Rezerwuj Retreat
                </Link>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Spacer for fixed navbar */}
      <div className="h-20"></div>
    </>
  );
}