/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./src/**/*.{js,jsx}'],
  theme: {
    extend: {
      colors: {
        // =============================================
        // 🏛️ BAKASANA - UNIFIED COLOR SYSTEM
        // Using CSS custom properties from design-tokens.css
        // =============================================

        // CORE WARM NEUTRALS - From design-tokens.css
        sanctuary: 'var(--sanctuary)',
        parchment: 'var(--parchment)',
        linen: 'var(--linen)',
        whisper: 'var(--whisper)',
        silk: 'var(--silk)',

        // WARM TEXT COLORS - WCAG AA Compliant
        charcoal: 'var(--charcoal)',
        'charcoal-light': 'var(--charcoal-light)',
        ash: 'var(--ash)',
        sage: 'var(--sage)',
        stone: 'var(--stone)',
        'stone-light': 'var(--stone-light)',

        // ENTERPRISE WARM ACCENTS
        'enterprise-brown': 'var(--enterprise-brown)',
        'temple-gold': 'var(--temple-gold)',
        terra: 'var(--terra)',
        sand: 'var(--sand)',
        amber: 'var(--amber)',

        // PURE CONTRASTS
        'pure-white': 'var(--pure-white)',
        'soft-black': 'var(--soft-black)',

        // EXTENDED WARM & FRIENDLY COLORS - GRADIENT ŚWITU
        cream: 'var(--cream)',
        'warm-peach': 'var(--warm-peach)',
        'soft-sage': 'var(--soft-sage)',
        terracotta: 'var(--terracotta)',
        blush: 'var(--blush)',
        'friendly-coral': 'var(--friendly-coral)',
        'calm-lavender': 'var(--calm-lavender)',
        
        // ENHANCED ROSE GOLD SYSTEM
        'rose-gold': 'var(--rose-gold)',
        'rose-gold-light': 'var(--rose-gold-light)',
        'rose-gold-warm': 'var(--rose-gold-warm)',
        'dawn-cream': 'var(--dawn-cream)',
        'dawn-peach': 'var(--dawn-peach)',
        'warm-sand': 'var(--warm-sand)',
        'morning-mist': 'var(--morning-mist)',
        'golden-hour': 'var(--golden-hour)',
        'sunset-glow': 'var(--sunset-glow)',

        // SYSTEM ALIASES - Simplified
        primary: 'var(--charcoal)',
        secondary: 'var(--ash)',
        accent: 'var(--enterprise-brown)',
        background: 'var(--sanctuary)',
        surface: 'var(--silk)',
        muted: 'var(--sage)',
        border: 'var(--border-light)',
      },
      fontFamily: {
        // UNIFIED TYPOGRAPHY SYSTEM - Using design-tokens.css
        'primary': 'var(--font-primary)',
        'secondary': 'var(--font-secondary)',
        'accent': 'var(--font-accent)',

        // OLD MONEY TYPOGRAPHY SYSTEM - Elegant serifs
        'playfair': ['var(--font-playfair)', 'Playfair Display', 'Times New Roman', 'serif'],
        'crimson': ['var(--font-secondary)', 'Crimson Pro', 'EB Garamond', 'Georgia', 'serif'],
        'montserrat': ['var(--font-montserrat)', 'Crimson Pro', 'Georgia', 'serif'],
        'lato': ['var(--font-secondary)', 'Crimson Pro', 'EB Garamond', 'Georgia', 'serif'],
        'caveat': ['var(--font-accent)', 'Playfair Display', 'serif'],

        // Legacy aliases for backward compatibility - mapped to elegant serifs
        'cormorant': ['Playfair Display', 'Times New Roman', 'serif'], 
        'inter': ['Crimson Pro', 'EB Garamond', 'Georgia', 'serif'], 
        'didot': ['Playfair Display', 'Times New Roman', 'serif'],
        'helvetica': ['Crimson Pro', 'Georgia', 'serif'],
      },
      fontSize: {
        // UNIFIED TYPOGRAPHY SCALE - Using design-tokens.css
        'hero': 'var(--text-hero)',
        'display-xl': 'var(--text-display-xl)',
        'display': 'var(--text-display)',
        'heading-lg': 'var(--text-heading-lg)',
        'heading': 'var(--text-heading)',
        'subtitle': 'var(--text-subtitle)',
        'body-lg': 'var(--text-body-lg)',
        'body': 'var(--text-body)',
        'caption': 'var(--text-caption)',
        'small': 'var(--text-small)',
        'micro': 'var(--text-micro)',

        // Legacy aliases for backward compatibility
        'hero-massive': ['clamp(100px, 15vw, 200px)', { lineHeight: '0.95', letterSpacing: '0.15em', fontWeight: '300' }],
        'hero-title': ['4rem', { lineHeight: '1.1', letterSpacing: '0.2em', fontWeight: '300' }],
      },
      letterSpacing: {
        // UNIFIED LETTER SPACING - Using design-tokens.css
        'tightest': 'var(--tracking-tightest)',
        'tight': 'var(--tracking-tight)',
        'normal': 'var(--tracking-normal)',
        'wide': 'var(--tracking-wide)',
        'wider': 'var(--tracking-wider)',
        'widest': 'var(--tracking-widest)',
        'ultra': 'var(--tracking-ultra)',
      },
      lineHeight: {
        // UNIFIED LINE HEIGHTS - Using design-tokens.css
        'none': 'var(--leading-none)',
        'tight': 'var(--leading-tight)',
        'snug': 'var(--leading-snug)',
        'normal': 'var(--leading-normal)',
        'relaxed': 'var(--leading-relaxed)',
        'loose': 'var(--leading-loose)',
        'breathing': 'var(--leading-breathing)',
      },
      spacing: {
        // UNIFIED SPACING SYSTEM - Using design-tokens.css
        'xs': 'var(--space-xs)',
        'sm': 'var(--space-sm)',
        'md': 'var(--space-md)',
        'lg': 'var(--space-lg)',
        'xl': 'var(--space-xl)',
        '2xl': 'var(--space-2xl)',
        '3xl': 'var(--space-3xl)',
        '4xl': 'var(--space-4xl)',

        // SPECIALIZED SPACING
        'section': 'var(--space-section)',
        'container': 'var(--space-container)',
        'breathe': 'var(--space-breathe)',
        'card': 'var(--space-card)',
        'hero-padding': 'var(--space-hero)',
        'nav-height': 'var(--space-nav)',
      },
      screens: {
        // BAKASANA ENTERPRISE BREAKPOINTS - Aligned with CSS
        'xs': '480px',           // Small mobile to mobile
        'sm': '768px',           // Mobile to tablet
        'md': '1024px',          // Tablet to desktop
        'lg': '1440px',          // Desktop to large
        'xl': '1920px',          // Large to ultra
      },
      container: {
        center: true,
        padding: {
          DEFAULT: '5%',         // 5% na mobile
          'sm': '6%',            // 6% na tablet
          'md': '8%',            // 8% na desktop
          'lg': '10%',           // 10% na large
          'xl': '12%',           // 12% na ultra
        },
        screens: {
          'xs': '480px',
          'sm': '768px',
          'md': '1024px',
          'lg': '1440px',
          'xl': '1920px',
        },
      },
      animation: {
        'fade-in': 'fadeIn 0.6s ease-out',
        'slide-up': 'slideUp 0.8s ease-out',
        'lotus-pulse': 'lotusPulse 3s ease-in-out infinite',
        'fade-in-up': 'fadeInUp 0.8s cubic-bezier(0.23, 1, 0.32, 1) forwards',
        'scale-in': 'scaleIn 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards',
        'slide-in-right': 'slideInRight 0.6s cubic-bezier(0.23, 1, 0.32, 1) forwards',
        'float': 'float 3s ease-in-out infinite',
        'pulse-gentle': 'pulseGentle 2s ease-in-out infinite',
        'shimmer': 'shimmer 2s ease-in-out infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0', transform: 'translateY(20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        slideUp: {
          '0%': { opacity: '0', transform: 'translateY(40px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        lotusPulse: {
          '0%, 100%': { opacity: '0.6', transform: 'scale(1)' },
          '50%': { opacity: '1', transform: 'scale(1.05)' },
        },
        fadeInUp: {
          '0%': { opacity: '0', transform: 'translateY(30px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        scaleIn: {
          '0%': { opacity: '0', transform: 'scale(0.8)' },
          '100%': { opacity: '1', transform: 'scale(1)' },
        },
        slideInRight: {
          '0%': { opacity: '0', transform: 'translateX(30px)' },
          '100%': { opacity: '1', transform: 'translateX(0)' },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        pulseGentle: {
          '0%, 100%': { opacity: '0.8', transform: 'scale(1)' },
          '50%': { opacity: '1', transform: 'scale(1.02)' },
        },
        shimmer: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(100%)' },
        },
      },
      // UNIFIED SHADOW SYSTEM - Using design-tokens.css
      boxShadow: {
        // WARM SHADOW SYSTEM - From design-tokens.css
        'subtle': 'var(--shadow-subtle)',
        'elegant': 'var(--shadow-elegant)',
        'elevated': 'var(--shadow-elevated)',
        'premium': 'var(--shadow-premium)',
        'hero': 'var(--shadow-hero)',

        // INTERACTIVE SHADOWS
        'hover': 'var(--shadow-hover)',
        'focus': 'var(--shadow-focus)',

        // Legacy aliases for backward compatibility
        'premium-shadow': 'var(--shadow-premium)',
        'warm-shadow': 'var(--shadow-elevated)',
      },
      // UNIFIED ANIMATION SYSTEM - Using design-tokens.css
      transitionDuration: {
        'instant': 'var(--duration-instant)',
        'fast': 'var(--duration-fast)',
        'normal': 'var(--duration-normal)',
        'slow': 'var(--duration-slow)',
        'entrance': 'var(--duration-entrance)',
      },
      transitionTimingFunction: {
        'smooth': 'var(--ease-smooth)',
        'spring': 'var(--ease-spring)',
        'bounce': 'var(--ease-bounce)',
        'gentle': 'var(--ease-gentle)',
      },
    },
  },
  plugins: [
    function({ addUtilities }) {
      const newUtilities = {
        '.whatsapp-elegant': {
          backgroundColor: '#8B7355',
          '&:hover': {
            backgroundColor: 'rgba(139, 115, 85, 0.9)',
          },
        },
        '.whatsapp-float': {
          position: 'fixed',
          bottom: '2.5rem',
          right: '2.5rem',
          boxShadow: '0 4px 12px rgba(139, 115, 85, 0.15)',
          zIndex: '998',
          '@media (max-width: 768px)': {
            bottom: '1.5rem',
            right: '1.5rem',
          },
        },
        '.whatsapp-icon': {
          width: '1.25rem',
          height: '1.25rem',
          color: 'white',
        },
      }
      addUtilities(newUtilities)
    }
  ],
}